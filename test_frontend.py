#!/usr/bin/env python3
"""
测试新前端的引用功能
"""

import requests
import json
import time

def test_chat_with_citations():
    """测试带引用的聊天功能"""
    url = "http://localhost:8000/api/chat"
    
    # 测试一个应该有引用的问题
    payload = {
        "id": f"test_frontend_{int(time.time())}",
        "messages": [
            {
                "role": "user",
                "content": "电子发票重复报销如何防范？"
            }
        ],
        "data": {}
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("🚀 测试前端引用功能...")
        print("发送请求:", json.dumps(payload, ensure_ascii=False, indent=2))
        print("-" * 60)
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"✅ 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_text = response.text
            print("📄 原始响应:")
            print(response_text[:500] + "..." if len(response_text) > 500 else response_text)
            print("-" * 60)
            
            # 检查引用数据
            if "CITATION_DATA:" in response_text:
                print("🎯 发现引用数据!")
                
                # 模拟前端处理
                import re
                citation_match = re.search(r'<!-- CITATION_DATA: (.*?) -->', response_text, re.DOTALL)
                if citation_match:
                    citation_json_str = citation_match.group(1)
                    try:
                        citation_data = json.loads(citation_json_str)
                        print("📊 引用数据解析成功:")
                        
                        for node_id, citation in citation_data.items():
                            print(f"  引用 #{citation['rank']}:")
                            print(f"    文档: {citation['filename']}")
                            print(f"    相似度: {round(citation['similarity_score'] * 100)}%")
                            print(f"    内容预览: {citation['content'][:100]}...")
                            print()
                        
                        # 测试前端处理逻辑
                        clean_text = response_text.replace(re.search(r'<!-- CITATION_DATA:.*?-->', response_text, re.DOTALL).group(0), '').strip()
                        print("🔧 清理后的文本:")
                        print(clean_text)
                        
                        # 检查Source引用
                        source_pattern = r'Source (\d+):'
                        sources = re.findall(source_pattern, clean_text)
                        if sources:
                            print(f"🔗 发现 {len(sources)} 个Source引用: {sources}")
                        else:
                            print("⚠️  未发现Source引用模式")
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ 引用数据JSON解析失败: {e}")
                else:
                    print("❌ 未找到引用数据匹配")
            else:
                print("⚠️  响应中没有引用数据")
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print("错误内容:", response.text)
            
    except Exception as e:
        print(f"💥 测试失败: {e}")

def test_frontend_files():
    """测试前端文件是否可访问"""
    base_url = "http://localhost:8000"
    
    files_to_test = [
        "/",  # 主页
        "/static/app.js",  # Vue应用
        "/static/citation-processor.js",  # 引用处理器
    ]
    
    print("🌐 测试前端文件访问...")
    
    for file_path in files_to_test:
        try:
            response = requests.get(base_url + file_path, timeout=10)
            status = "✅" if response.status_code == 200 else "❌"
            print(f"{status} {file_path}: {response.status_code}")
        except Exception as e:
            print(f"❌ {file_path}: 错误 - {e}")

if __name__ == "__main__":
    print("🧪 开始测试新前端...")
    print("=" * 60)
    
    # 测试文件访问
    test_frontend_files()
    print()
    
    # 测试引用功能
    test_chat_with_citations()
    
    print("=" * 60)
    print("✨ 测试完成！")
    print()
    print("💡 提示:")
    print("1. 打开浏览器访问 http://localhost:8000")
    print("2. 尝试问一个问题，如：'电子发票重复报销如何防范？'")
    print("3. 观察引用数字是否出现，鼠标悬浮是否显示文档内容")
