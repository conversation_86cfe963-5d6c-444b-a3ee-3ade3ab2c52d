#!/usr/bin/env python3
"""
调试API响应格式
"""

import requests
import json
import time
import re

def debug_api_response():
    """调试API响应"""
    url = "http://localhost:8000/api/chat"
    
    payload = {
        "id": f"debug_{int(time.time())}",
        "messages": [
            {
                "role": "user",
                "content": "电子发票重复报销如何防范？"
            }
        ],
        "data": {}
    }
    
    try:
        response = requests.post(url, json=payload, headers={"Content-Type": "application/json"}, timeout=30)
        
        if response.status_code == 200:
            response_text = response.text
            print("=== 完整API响应 ===")
            print(response_text)
            print("\n" + "="*50 + "\n")
            
            # 查找引用数据
            citation_pattern = r'<!-- CITATION_DATA: (.*?) -->'
            citation_match = re.search(citation_pattern, response_text, re.DOTALL)
            
            if citation_match:
                citation_json_str = citation_match.group(1)
                print("=== 提取的引用JSON字符串 ===")
                print(repr(citation_json_str))
                print("\n" + "="*50 + "\n")
                
                # 尝试解析JSON
                try:
                    citation_data = json.loads(citation_json_str)
                    print("=== 解析成功的引用数据 ===")
                    print(json.dumps(citation_data, ensure_ascii=False, indent=2))
                except json.JSONDecodeError as e:
                    print(f"=== JSON解析错误 ===")
                    print(f"错误: {e}")
                    print(f"位置: {e.pos}")
                    print(f"错误字符: {repr(citation_json_str[max(0, e.pos-10):e.pos+10])}")
                    
                    # 尝试修复常见问题
                    print("\n=== 尝试修复JSON ===")
                    
                    # 移除可能的转义问题
                    try:
                        # 方法1: 直接解码
                        fixed_json = citation_json_str.encode().decode('unicode_escape')
                        citation_data = json.loads(fixed_json)
                        print("✅ 修复成功 (unicode_escape)")
                        print(json.dumps(citation_data, ensure_ascii=False, indent=2))
                    except:
                        try:
                            # 方法2: 替换转义字符
                            fixed_json = citation_json_str.replace('\\n', ' ').replace('\\r', '').replace('\\"', '"')
                            citation_data = json.loads(fixed_json)
                            print("✅ 修复成功 (替换转义)")
                            print(json.dumps(citation_data, ensure_ascii=False, indent=2))
                        except Exception as e2:
                            print(f"❌ 修复失败: {e2}")
            else:
                print("❌ 未找到引用数据")
                
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"💥 请求失败: {e}")

if __name__ == "__main__":
    debug_api_response()
