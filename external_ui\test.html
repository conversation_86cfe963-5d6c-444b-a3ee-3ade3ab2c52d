<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>引用功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 引用悬浮框样式 */
        .citation-tooltip {
            position: absolute;
            z-index: 1000;
            max-width: 400px;
            max-height: 300px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 12px;
            font-size: 14px;
            line-height: 1.5;
            overflow-y: auto;
            pointer-events: none;
        }
        
        /* 引用数字样式 */
        .citation-number {
            display: inline-block;
            background: #3b82f6;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            text-align: center;
            line-height: 20px;
            font-size: 12px;
            font-weight: bold;
            margin: 0 2px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .citation-number:hover {
            background: #2563eb;
            transform: scale(1.1);
        }
        
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">引用功能测试</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">测试引用处理器</h2>
            <button id="testBtn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                测试引用解析
            </button>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">处理结果</h2>
            <div id="result" class="border rounded p-4 min-h-32 bg-gray-50"></div>
        </div>
        
        <!-- 引用悬浮框 -->
        <div id="tooltip" class="citation-tooltip custom-scrollbar" style="display: none;">
            <div id="tooltipContent"></div>
        </div>
    </div>

    <script src="/static/citation-processor.js"></script>
    <script>
        // 模拟API响应数据
        const mockResponse = `电子发票重复报销的防范措施包括以下几个方面：

1. **风险识别**：识别重复报销的常见形式，如电子版与纸质版重复报销、多次打印同一张发票、在不同系统中重复提交同一张发票等【1】。

2. **内控制度建设**：
   - 建立发票接收、审核和归档管理制度，确保每张发票都有唯一的标识，并进行编号管理【2】【3】。
   - 实施多级审核机制，设置发票真伪验证环节和重复发票识别检查点【1】。

3. **技术防范手段**：
   - 开发发票自动查重系统，基于发票代码号码进行查重，并设置重复发票预警机制【2】。
   - 集成税务局发票查验接口，实现发票真伪的自动验证【3】。

4. **报销流程控制**：
   - 在报销申请环节要求提供原始电子发票文件，禁止使用复印件或截图【2】。
   - 在审核批准环节设置发票验证审核岗位，建立发票重复性检查程序【2】。

5. **培训与宣传**：对员工进行电子发票管理培训，提高合规意识，确保每位员工了解相关制度和流程【3】。

通过这些措施，可以有效降低电子发票重复报销的风险，维护企业的财务安全和合规性。

<!-- CITATION_DATA: {
  "6c0698a7-8524-47df-b936-2676ef83140c": {
    "rank": 1,
    "filename": "常见问题类-04_电子发票重复报销防范措施.txt",
    "content": "Source 1: # 电子发票重复报销防范措施  ## 概述  本文档详细介绍电子发票重复报销的风险识别和防范措施，包括重复报销的常见形式、识别方法、内控制度建设以及技术防范手段，适用于企业财务人员建立电子发票管理制度。",
    "similarity_score": 0.6144272664165925
  },
  "a7664b56-6a4b-4ac8-9607-66dd0eb559f9": {
    "rank": 2,
    "filename": "常见问题类-04_电子发票重复报销防范措施.txt",
    "content": "Source 2: **发票归档制度**    - 建立电子发票归档管理制度    - 采用电子化归档方式    - 建立发票查询检索系统  #### 报销流程控制  1. **报销申请环节**     - 要求提供原始电子发票文件    - 禁止使用复印件或截图报销",
    "similarity_score": 0.5810965627245467
  },
  "b96d06f4-418c-424d-84a9-7743f2998b35": {
    "rank": 3,
    "filename": "常见问题类-04_电子发票重复报销防范措施.txt",
    "content": "Source 3: **发票归档**    - 将电子发票归档保存    - 建立发票与凭证的关联关系    - 设置发票查询权限  ### 实际案例分析  **案例：某科技公司电子发票重复报销防范实践**",
    "similarity_score": 0.5269393184653601
  }
} -->`;

        // 全局悬浮框函数
        window.showCitationTooltip = function(event, rank) {
            const citation = window.citationProcessor.getCitationByRank(rank);
            if (!citation) return;

            const tooltip = document.getElementById('tooltip');
            const tooltipContent = document.getElementById('tooltipContent');
            
            const rect = event.target.getBoundingClientRect();
            tooltip.style.left = rect.left + window.scrollX + 'px';
            tooltip.style.top = rect.bottom + window.scrollY + 5 + 'px';
            tooltip.style.display = 'block';
            
            const filename = window.citationProcessor.formatFilename(citation.filename);
            const similarity = window.citationProcessor.formatSimilarityScore(citation.similarity_score);
            const content = citation.content.replace(/^Source \d+: /, '');
            
            tooltipContent.innerHTML = `
                <div class="font-semibold text-gray-900 mb-2">
                    📄 引用 #${rank}
                </div>
                <div class="text-sm text-gray-600 mb-2">
                    <strong>文档:</strong> ${filename}
                </div>
                <div class="text-sm text-gray-600 mb-2">
                    <strong>相似度:</strong> ${similarity}
                </div>
                <div class="text-sm text-gray-700 leading-relaxed">
                    ${content}
                </div>
            `;
        };

        window.hideCitationTooltip = function() {
            const tooltip = document.getElementById('tooltip');
            tooltip.style.display = 'none';
        };

        // 测试按钮事件
        document.getElementById('testBtn').addEventListener('click', function() {
            console.log('开始测试引用处理器...');
            
            const result = window.citationProcessor.processResponse(mockResponse);
            
            console.log('处理结果:', result);
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div class="mb-4">
                    <h3 class="font-semibold mb-2">处理后的文本:</h3>
                    <div class="p-3 bg-white border rounded">${result.text}</div>
                </div>
                <div class="mb-4">
                    <h3 class="font-semibold mb-2">引用数据:</h3>
                    <pre class="text-xs bg-gray-100 p-2 rounded overflow-auto">${JSON.stringify(result.citations, null, 2)}</pre>
                </div>
                <div>
                    <h3 class="font-semibold mb-2">状态:</h3>
                    <p>有引用: ${result.hasCitations ? '是' : '否'}</p>
                    <p>引用数量: ${Object.keys(result.citations).length}</p>
                </div>
            `;
        });
    </script>
</body>
</html>
