/**
 * 引用处理器 - 处理API响应中的引用数据
 */
class CitationProcessor {
  constructor() {
    this.citationData = {};
  }

  /**
   * 处理API响应，提取引用数据并替换引用标记
   * @param {string} responseText - API原始响应文本
   * @returns {object} 处理后的结果
   */
  processResponse(responseText) {
    try {
      // 提取引用数据
      const citationMatch = responseText.match(
        /<!-- CITATION_DATA: (.*?) -->/s
      );
      if (citationMatch) {
        const citationJsonStr = citationMatch[1];
        try {
          // 解析引用数据
          this.citationData = JSON.parse(citationJsonStr);
          console.log("解析到引用数据:", this.citationData);
        } catch (e) {
          console.error("引用数据JSON解析失败:", e);
          this.citationData = {};
        }
      } else {
        this.citationData = {};
      }

      // 移除引用数据注释，获取纯文本
      const cleanText = responseText
        .replace(/<!-- CITATION_DATA:.*?-->/s, "")
        .trim();

      // 处理引用标记
      const processedText = this.replaceSourceReferences(cleanText);

      return {
        text: processedText,
        citations: this.citationData,
        hasCitations: Object.keys(this.citationData).length > 0,
      };
    } catch (error) {
      console.error("处理响应时出错:", error);
      return {
        text: responseText,
        citations: {},
        hasCitations: false,
      };
    }
  }

  /**
   * 替换文本中的Source引用为可点击的引用数字
   * @param {string} text - 原始文本
   * @returns {string} 处理后的HTML文本
   */
  replaceSourceReferences(text) {
    // 查找所有的Source引用模式
    const sourcePattern = /Source (\d+):/g;
    let match;
    const sourceNumbers = [];

    while ((match = sourcePattern.exec(text)) !== null) {
      sourceNumbers.push(parseInt(match[1]));
    }

    if (sourceNumbers.length === 0) {
      return this.escapeHtml(text);
    }

    // 为每个引用创建映射
    const citationMap = this.createCitationMap();

    // 替换Source引用为引用数字
    let processedText = text;
    sourceNumbers.forEach((sourceNum) => {
      const citationInfo = citationMap[sourceNum];
      if (citationInfo) {
        const citationHtml = this.createCitationHtml(sourceNum, citationInfo);
        const sourceRegex = new RegExp(`Source ${sourceNum}:`, "g");
        processedText = processedText.replace(sourceRegex, citationHtml);
      }
    });

    return this.escapeHtmlExceptCitations(processedText);
  }

  /**
   * 转义HTML但保留引用标记
   * @param {string} text - 包含引用标记的文本
   * @returns {string} 转义后的文本
   */
  escapeHtmlExceptCitations(text) {
    // 先提取所有引用标记
    const citationPattern = /<span class="citation-number"[^>]*>.*?<\/span>/g;
    const citations = [];
    let match;

    while ((match = citationPattern.exec(text)) !== null) {
      citations.push(match[0]);
    }

    // 用占位符替换引用标记
    let textWithPlaceholders = text;
    citations.forEach((citation, index) => {
      textWithPlaceholders = textWithPlaceholders.replace(
        citation,
        `__CITATION_${index}__`
      );
    });

    // 转义HTML
    const escapedText = this.escapeHtml(textWithPlaceholders);

    // 恢复引用标记
    let finalText = escapedText;
    citations.forEach((citation, index) => {
      finalText = finalText.replace(`__CITATION_${index}__`, citation);
    });

    return finalText;
  }

  /**
   * 创建引用映射（按rank排序）
   * @returns {object} 引用映射
   */
  createCitationMap() {
    const citationMap = {};

    // 按rank排序引用数据
    const sortedCitations = Object.entries(this.citationData).sort(
      ([, a], [, b]) => a.rank - b.rank
    );

    sortedCitations.forEach(([nodeId, citation]) => {
      citationMap[citation.rank] = {
        nodeId,
        ...citation,
      };
    });

    return citationMap;
  }

  /**
   * 创建引用HTML
   * @param {number} rank - 引用排序
   * @param {object} citation - 引用数据
   * @returns {string} 引用HTML
   */
  createCitationHtml(rank, citation) {
    const citationData = JSON.stringify(citation).replace(/"/g, "&quot;");
    return `<span class="citation-number"
                      data-rank="${rank}"
                      data-citation="${citationData}"
                      onmouseenter="window.showCitationTooltip(event, ${rank})"
                      onmouseleave="window.hideCitationTooltip()">${rank}</span>`;
  }

  /**
   * 转义HTML特殊字符
   * @param {string} text - 原始文本
   * @returns {string} 转义后的文本
   */
  escapeHtml(text) {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 获取引用数据
   * @param {number} rank - 引用排序
   * @returns {object|null} 引用数据
   */
  getCitationByRank(rank) {
    const citation = Object.values(this.citationData).find(
      (c) => c.rank === rank
    );
    return citation || null;
  }

  /**
   * 格式化文档名称
   * @param {string} filename - 原始文件名
   * @returns {string} 格式化后的文件名
   */
  formatFilename(filename) {
    return filename.replace(/^常见问题类-\d+_/, "").replace(/\.txt$/, "");
  }

  /**
   * 格式化相似度分数
   * @param {number} score - 相似度分数
   * @returns {string} 格式化后的分数
   */
  formatSimilarityScore(score) {
    return Math.round(score * 100) + "%";
  }
}

// 全局引用处理器实例
window.citationProcessor = new CitationProcessor();
